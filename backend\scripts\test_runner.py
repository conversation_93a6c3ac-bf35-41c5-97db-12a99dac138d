#!/usr/bin/env python3
"""
Test Runner Script for Ultimate Electrical Designer Backend

This script provides convenient commands for running different categories of tests
as defined in pyproject.toml. It supports the Test Suite Quality Improvement Action Plan.

Usage:
    python scripts/test_runner.py --help
    python scripts/test_runner.py unit
    python scripts/test_runner.py integration
    python scripts/test_runner.py security
    python scripts/test_runner.py all --coverage
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path
from typing import List, Optional


class TestRunner:
    """Test runner for different test categories."""

    def __init__(self, backend_dir: Path):
        self.backend_dir = backend_dir
        self.base_cmd = ["python", "-m", "pytest"]

    def run_command(self, cmd: List[str], description: str) -> bool:
        """Run a command and return success status."""
        print(f"\n{'=' * 60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(cmd)}")
        print(f"Working Directory: {self.backend_dir}")
        print(f"{'=' * 60}")

        try:
            result = subprocess.run(
                cmd, cwd=self.backend_dir, check=True, capture_output=True, text=True
            )
            print(f"✅ {description} completed successfully")
            if result.stdout:
                print("STDOUT:")
                print(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} failed with exit code {e.returncode}")
            if e.stdout:
                print("STDOUT:")
                print(e.stdout)
            if e.stderr:
                print("STDERR:")
                print(e.stderr)
            return False

    def unit_tests(self, coverage: bool = False) -> bool:
        """Run unit tests."""
        cmd = self.base_cmd + ["-m", "unit", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core",
                    "--cov=api",
                    "--cov=config",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/unit",
                ]
            )

        return self.run_command(cmd, "Unit Tests")

    def integration_tests(self, coverage: bool = False) -> bool:
        """Run integration tests."""
        cmd = self.base_cmd + ["-m", "integration", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core",
                    "--cov=api",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/integration",
                ]
            )

        return self.run_command(cmd, "Integration Tests")

    def api_tests(self, coverage: bool = False) -> bool:
        """Run API tests."""
        cmd = self.base_cmd + ["-m", "api", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=api",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/api",
                ]
            )

        return self.run_command(cmd, "API Tests")

    def database_tests(self, coverage: bool = False) -> bool:
        """Run database tests."""
        cmd = self.base_cmd + ["-m", "database", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/repositories",
                    "--cov=core/database",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/database",
                ]
            )

        return self.run_command(cmd, "Database Tests")

    def security_tests(self) -> bool:
        """Run security tests."""
        cmd = self.base_cmd + ["-m", "security", "--tb=short", "-v"]

        return self.run_command(cmd, "Security Tests")

    def performance_tests(self) -> bool:
        """Run performance tests."""
        cmd = self.base_cmd + [
            "-m",
            "performance",
            "--tb=short",
            "-v",
            "--benchmark-only",
        ]

        return self.run_command(cmd, "Performance Tests")

    def smoke_tests(self, coverage: bool = False) -> bool:
        """Run smoke tests."""
        cmd = self.base_cmd + ["-m", "smoke", "--tb=line", "-v", "--maxfail=5"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core",
                    "--cov=api",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/smoke",
                ]
            )

        return self.run_command(cmd, "Smoke Tests")

    def schema_tests(self, coverage: bool = False) -> bool:
        """Run schema validation tests."""
        cmd = self.base_cmd + ["-m", "schema", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/schemas",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/schemas",
                ]
            )

        return self.run_command(cmd, "Schema Tests")

    def service_tests(self, coverage: bool = False) -> bool:
        """Run service layer tests."""
        cmd = self.base_cmd + ["-m", "service", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/services",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/services",
                ]
            )

        return self.run_command(cmd, "Service Tests")

    def repository_tests(self, coverage: bool = False) -> bool:
        """Run repository layer tests."""
        cmd = self.base_cmd + ["-m", "repository", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/repositories",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/repositories",
                ]
            )

        return self.run_command(cmd, "Repository Tests")

    def calculations_tests(self, coverage: bool = False) -> bool:
        """Run calculations tests."""
        cmd = self.base_cmd + ["-m", "calculations", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/calculations",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/calculations",
                ]
            )

        return self.run_command(cmd, "Calculations Tests")

    def import_export_tests(self, coverage: bool = False) -> bool:
        """Run import/export tests."""
        cmd = self.base_cmd + ["-m", "import_export", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/import_export",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/import_export",
                ]
            )

        return self.run_command(cmd, "Import/Export Tests")

    def reports_tests(self, coverage: bool = False) -> bool:
        """Run reports tests."""
        cmd = self.base_cmd + ["-m", "reports", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/reports",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/reports",
                ]
            )

        return self.run_command(cmd, "Reports Tests")

    def standards_tests(self, coverage: bool = False) -> bool:
        """Run standards tests."""
        cmd = self.base_cmd + ["-m", "standards", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core/standards",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov/standards",
                ]
            )

        return self.run_command(cmd, "Standards Tests")

    def entity_tests(self, entity: str, coverage: bool = False) -> bool:
        """Run tests for a specific entity."""
        cmd = self.base_cmd + ["-m", entity, "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    f"--cov=core/models/{entity}",
                    f"--cov=core/schemas/{entity}",
                    f"--cov=core/services/{entity}",
                    f"--cov=core/repositories/{entity}",
                    f"--cov=api/v1/{entity}",
                    "--cov-report=term-missing",
                    f"--cov-report=html:htmlcov/{entity}",
                ]
            )

        return self.run_command(cmd, f"{entity.title()} Entity Tests")

    def all_tests(self, coverage: bool = False) -> bool:
        """Run all tests."""
        cmd = self.base_cmd + ["tests/", "--tb=short", "-v"]
        if coverage:
            cmd.extend(
                [
                    "--cov=core",
                    "--cov=api",
                    "--cov=config",
                    "--cov-report=term-missing",
                    "--cov-report=html:htmlcov",
                    "--cov-report=xml:coverage.xml",
                    "--cov-fail-under=90",
                ]
            )

        return self.run_command(cmd, "All Tests")

    def critical_fixes_tests(self, coverage: bool = False) -> bool:
        """Run tests for critical fixes identified in Phase 1."""
        print("\n🔧 Running Critical Fixes Tests (Phase 1)")
        print("=" * 60)

        # Test specific failing tests mentioned in analysis
        critical_tests = [
            "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_success",
            "tests/test_api/test_electrical_routes.py::TestElectricalNodeRoutes::test_create_electrical_node_validation_error",
            "tests/test_api/test_component_routes.py::TestComponentRoutes::test_create_component_success",
        ]

        success = True
        for test in critical_tests:
            cmd = self.base_cmd + [test, "--tb=short", "-v", "-s"]
            if coverage:
                cmd.extend(["--cov=api", "--cov-report=term-missing"])

            test_name = test.split("::")[-1]
            success &= self.run_command(cmd, f"Critical Test: {test_name}")

        return success

    def diagnostic_test(self, test_path: str) -> bool:
        """Run a single test with maximum diagnostic output."""
        cmd = self.base_cmd + [
            test_path,
            "--tb=long",
            "-v",
            "-s",
            "--capture=no",
            "--log-cli-level=DEBUG",
        ]

        return self.run_command(cmd, f"Diagnostic Test: {test_path}")

    def quality_check(self) -> bool:
        """Run code quality checks."""
        success = True

        # Ruff linting
        ruff_cmd = ["ruff", "check", ".", "--fix"]
        success &= self.run_command(ruff_cmd, "Ruff Linting")

        # Ruff formatting
        format_cmd = ["ruff", "format", "."]
        success &= self.run_command(format_cmd, "Ruff Formatting")

        # MyPy type checking
        mypy_cmd = ["mypy", "core", "api", "config"]
        success &= self.run_command(mypy_cmd, "MyPy Type Checking")

        # Bandit security check
        bandit_cmd = [
            "bandit",
            "-r",
            "core",
            "api",
            "config",
            "-f",
            "json",
            "-o",
            "bandit-report.json",
        ]
        success &= self.run_command(bandit_cmd, "Bandit Security Check")

        return success


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Test runner for Ultimate Electrical Designer Backend",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Test Categories:
  unit          Run unit tests
  integration   Run integration tests
  api           Run API tests
  database      Run database tests
  security      Run security tests
  performance   Run performance tests
  smoke         Run smoke tests
  schema        Run schema validation tests
  service       Run service layer tests
  repository    Run repository layer tests
  calculations  Run calculations tests
  import_export Run import/export tests
  reports       Run reports tests
  standards     Run standards tests
  all           Run all tests
  quality       Run code quality checks
  critical      Run critical fixes tests (Phase 1)

Entity-specific tests:
  project       Run project entity tests
  component     Run component entity tests
  heat_tracing  Run heat tracing entity tests
  electrical    Run electrical entity tests
  switchboard   Run switchboard entity tests
  user          Run user entity tests
  document      Run document entity tests

Examples:
  python scripts/test_runner.py unit --coverage
  python scripts/test_runner.py integration
  python scripts/test_runner.py project --coverage
  python scripts/test_runner.py all --coverage
  python scripts/test_runner.py quality
  python scripts/test_runner.py critical --coverage
        """,
    )

    parser.add_argument(
        "test_type",
        choices=[
            "unit",
            "integration",
            "api",
            "database",
            "security",
            "performance",
            "smoke",
            "schema",
            "service",
            "repository",
            "calculations",
            "import_export",
            "reports",
            "standards",
            "all",
            "quality",
            "critical",
            "project",
            "component",
            "heat_tracing",
            "electrical",
            "switchboard",
            "user",
            "document",
        ],
        help="Type of tests to run",
    )

    parser.add_argument(
        "--coverage", action="store_true", help="Include coverage reporting"
    )

    parser.add_argument(
        "--backend-dir",
        type=Path,
        default=Path(__file__).parent.parent,
        help="Backend directory path",
    )

    args = parser.parse_args()

    # Set environment variables
    os.environ["PYTHONPATH"] = str(args.backend_dir)
    os.environ["DATABASE_URL"] = "sqlite:///:memory:"
    os.environ["TESTING"] = "true"

    runner = TestRunner(args.backend_dir)

    # Entity tests
    entities = [
        "project",
        "component",
        "heat_tracing",
        "electrical",
        "switchboard",
        "user",
        "document",
    ]
    if args.test_type in entities:
        success = runner.entity_tests(args.test_type, args.coverage)
    else:
        # Test category mapping
        test_methods = {
            "unit": runner.unit_tests,
            "integration": runner.integration_tests,
            "api": runner.api_tests,
            "database": runner.database_tests,
            "security": runner.security_tests,
            "performance": runner.performance_tests,
            "smoke": runner.smoke_tests,
            "schema": runner.schema_tests,
            "service": runner.service_tests,
            "repository": runner.repository_tests,
            "calculations": runner.calculations_tests,
            "import_export": runner.import_export_tests,
            "reports": runner.reports_tests,
            "standards": runner.standards_tests,
            "all": runner.all_tests,
            "quality": runner.quality_check,
            "critical": runner.critical_fixes_tests,
        }

        method = test_methods[args.test_type]
        if args.test_type in ["quality"]:
            success = method()
        else:
            success = method(args.coverage)

    if success:
        print(f"\n✅ {args.test_type.title()} tests completed successfully!")
        sys.exit(0)
    else:
        print(f"\n❌ {args.test_type.title()} tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
